#!/usr/bin/env python3
"""
Test script for SAITS with problematic data (lots of missing values)
"""

import numpy as np
import pandas as pd
from ml_core import MODEL_REGISTRY, impute_logs_deep

def test_saits_difficult_data():
    """Test SAITS with very fragmented data that might cause sequence creation issues"""
    print("🔍 Testing SAITS with difficult/fragmented data...")
    
    # Create data with lots of missing sections (worst case scenario)
    np.random.seed(42)
    n_points_per_well = 200  # Smaller wells
    
    df = pd.DataFrame({
        'WELL': ['WELL_A'] * n_points_per_well + ['WELL_B'] * n_points_per_well,
        'MD': np.concatenate([
            np.linspace(1000, 1500, n_points_per_well),
            np.linspace(1500, 2000, n_points_per_well)
        ]),
        'GR': np.random.normal(50, 15, n_points_per_well * 2),
        'NPHI': np.random.normal(0.2, 0.05, n_points_per_well * 2),
        'RHOB': np.random.normal(2.3, 0.2, n_points_per_well * 2),
        'P-WAVE': np.random.normal(200, 30, n_points_per_well * 2)
    })
    
    # Add lots of missing sections to make sequence creation difficult
    for well in df['WELL'].unique():
        well_mask = df['WELL'] == well
        well_indices = df[well_mask].index
        
        # Add many short missing sections
        for i in range(15):  # Many missing sections
            start_idx = np.random.choice(well_indices[5:-5])
            end_idx = start_idx + np.random.randint(3, 8)  # Short sections
            df.loc[start_idx:end_idx, 'P-WAVE'] = np.nan
    
    feature_cols = ['GR', 'NPHI', 'RHOB']
    target_col = 'P-WAVE'
    
    print(f"Data shape: {df.shape}")
    print(f"Missing values in target: {df[target_col].isna().sum()} ({df[target_col].isna().sum()/len(df)*100:.1f}%)")
    
    # Check continuous sections
    for well in df['WELL'].unique():
        well_data = df[df['WELL'] == well]
        is_complete = well_data[feature_cols + [target_col]].notna().all(axis=1)
        continuous_sections = []
        current_section = 0
        
        for i, complete in enumerate(is_complete):
            if complete:
                current_section += 1
            else:
                if current_section > 0:
                    continuous_sections.append(current_section)
                current_section = 0
        if current_section > 0:
            continuous_sections.append(current_section)
            
        print(f"Well {well}: Continuous sections: {continuous_sections} (max: {max(continuous_sections) if continuous_sections else 0})")
    
    # Test SAITS with default parameters
    saits_config = MODEL_REGISTRY['saits']
    hparams = {
        'n_features': 4,
        'sequence_len': 64,  # This might be too long for fragmented data
        'epochs': 10,  # Shorter for testing
        'batch_size': 16,
        'learning_rate': 1e-3,
        'n_layers': 2,
        'd_model': 128,
        'n_heads': 4,
        'dropout': 0.1
    }
    
    print(f"\nTesting with sequence length: {hparams['sequence_len']}")
    
    try:
        res_df, mres = impute_logs_deep(df, feature_cols, target_col, saits_config, hparams)
        
        if mres and res_df is not None:
            print("✅ SAITS completed successfully with difficult data!")
            print(f"   Result shape: {res_df.shape}")
            return True
        else:
            print("❌ SAITS failed to produce results")
            return False
            
    except Exception as e:
        print(f"❌ SAITS failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_saits_difficult_data()
    if success:
        print("\n✅ SAITS handles difficult data correctly!")
    else:
        print("\n❌ SAITS needs more robust handling for difficult data.")
