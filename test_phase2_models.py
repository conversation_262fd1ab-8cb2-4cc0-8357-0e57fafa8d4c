#!/usr/bin/env python3
"""
Comprehensive test suite for Phase 2 core models (SAITS and BRITS).
Tests model initialization, training, prediction, and integration with existing workflow.
"""

import unittest
import torch
import numpy as np
import warnings
from typing import Dict, Any

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

class TestPhase2CoreModels(unittest.TestCase):
    """Test suite for Phase 2 core model implementations."""
    
    def setUp(self):
        """Set up test data and parameters."""
        self.n_features = 4
        self.sequence_len = 32
        self.batch_size = 8
        self.epochs = 2  # Small for testing
        
        # Create realistic well log test data
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Generate synthetic well log data with realistic patterns
        self.test_data = self._generate_synthetic_well_log_data()
        self.truth_data = self._generate_synthetic_well_log_data(add_noise=False)
        
        # Add missing values to test data
        missing_mask = torch.rand_like(self.test_data) < 0.3  # 30% missing
        self.test_data[missing_mask] = float('nan')
        
        print(f"📊 Test data shape: {self.test_data.shape}")
        print(f"🔍 Missing values: {torch.isnan(self.test_data).sum().item()}")
    
    def _generate_synthetic_well_log_data(self, add_noise=True):
        """Generate synthetic well log data with realistic patterns."""
        # Create depth-based trends
        depth = torch.linspace(0, 1, self.sequence_len).unsqueeze(0).repeat(self.batch_size, 1).unsqueeze(-1)
        
        # GR (Gamma Ray) - typically 0-150 API
        gr = 50 + 30 * torch.sin(depth * 10) + 20 * torch.cos(depth * 5)
        
        # NPHI (Neutron Porosity) - typically 0-0.4 v/v
        nphi = 0.2 + 0.1 * torch.sin(depth * 8) + 0.05 * torch.cos(depth * 12)
        
        # RHOB (Bulk Density) - typically 1.5-3.0 g/cc
        rhob = 2.3 + 0.3 * torch.cos(depth * 6) + 0.1 * torch.sin(depth * 15)
        
        # Target log (e.g., resistivity) - correlated with other logs
        target = 10 + 5 * torch.log(gr / 50) + 20 * (0.3 - nphi) + 10 * (rhob - 2.0)
        
        # Combine features
        data = torch.cat([gr, nphi, rhob, target], dim=-1)
        
        # Add realistic noise
        if add_noise:
            noise = torch.randn_like(data) * 0.05
            data = data + noise
        
        # Ensure positive values where appropriate
        data[:, :, 0] = torch.clamp(data[:, :, 0], min=0)  # GR
        data[:, :, 1] = torch.clamp(data[:, :, 1], min=0, max=1)  # NPHI
        data[:, :, 2] = torch.clamp(data[:, :, 2], min=1.0, max=4.0)  # RHOB
        data[:, :, 3] = torch.clamp(data[:, :, 3], min=0.1)  # Target
        
        return data.float()
    
    def test_saits_model_availability(self):
        """Test SAITS model availability and import."""
        try:
            from models.advanced_models.saits_model import SAITSModel
            self.assertTrue(True, "SAITS model imported successfully")
        except ImportError as e:
            self.fail(f"SAITS model not available: {e}")
    
    def test_brits_model_availability(self):
        """Test BRITS model availability and import."""
        try:
            from models.advanced_models.brits_model import BRITSModel
            self.assertTrue(True, "BRITS model imported successfully")
        except ImportError as e:
            self.fail(f"BRITS model not available: {e}")
    
    def test_saits_model_initialization(self):
        """Test SAITS model initialization with various parameters."""
        from models.advanced_models.saits_model import SAITSModel
        
        # Test default initialization
        model = SAITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            epochs=self.epochs
        )
        
        self.assertEqual(model.n_features, self.n_features)
        self.assertEqual(model.sequence_len, self.sequence_len)
        self.assertEqual(model.epochs, self.epochs)
        self.assertFalse(model.is_fitted)
        
        # Test parameter validation
        with self.assertRaises(ValueError):
            SAITSModel(d_model=100, n_heads=3)  # d_model not divisible by n_heads
        
        with self.assertRaises(ValueError):
            SAITSModel(n_features=1)  # Too few features
    
    def test_brits_model_initialization(self):
        """Test BRITS model initialization with various parameters."""
        from models.advanced_models.brits_model import BRITSModel
        
        # Test default initialization
        model = BRITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            epochs=self.epochs
        )
        
        self.assertEqual(model.n_features, self.n_features)
        self.assertEqual(model.sequence_len, self.sequence_len)
        self.assertEqual(model.epochs, self.epochs)
        self.assertFalse(model.is_fitted)
        
        # Test custom parameters
        model_custom = BRITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            rnn_hidden_size=64,
            epochs=self.epochs
        )
        
        self.assertEqual(model_custom.rnn_hidden_size, 64)
    
    def test_saits_training_and_prediction(self):
        """Test SAITS model training and prediction workflow."""
        from models.advanced_models.saits_model import SAITSModel
        
        model = SAITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            n_layers=1,  # Small for testing
            d_model=64,  # Small for testing
            n_heads=2,
            epochs=self.epochs,
            batch_size=self.batch_size
        )
        
        # Test training
        print("🚀 Testing SAITS training...")
        model.fit(self.test_data, self.truth_data)
        self.assertTrue(model.is_fitted)
        
        # Test prediction
        print("🔮 Testing SAITS prediction...")
        predictions = model.predict(self.test_data)
        
        self.assertEqual(predictions.shape, self.test_data.shape)
        self.assertFalse(torch.isnan(predictions).all())
        
        # Test evaluation
        missing_mask = torch.isnan(self.test_data)
        metrics = model.evaluate_imputation(self.truth_data, predictions, missing_mask)
        
        self.assertIn('mae', metrics)
        self.assertIn('rmse', metrics)
        self.assertIn('r2', metrics)
        self.assertGreater(metrics['valid_points'], 0)
    
    def test_brits_training_and_prediction(self):
        """Test BRITS model training and prediction workflow."""
        from models.advanced_models.brits_model import BRITSModel
        
        model = BRITSModel(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            rnn_hidden_size=64,  # Small for testing
            epochs=self.epochs,
            batch_size=self.batch_size
        )
        
        # Test training
        print("🚀 Testing BRITS training...")
        model.fit(self.test_data, self.truth_data)
        self.assertTrue(model.is_fitted)
        
        # Test prediction
        print("🔮 Testing BRITS prediction...")
        predictions = model.predict(self.test_data)
        
        self.assertEqual(predictions.shape, self.test_data.shape)
        self.assertFalse(torch.isnan(predictions).all())
        
        # Test model complexity
        complexity = model.get_model_complexity()
        self.assertIn('total_parameters', complexity)
        self.assertEqual(complexity['complexity_score'], 2)
    
    def test_model_registry_integration(self):
        """Test that models are properly integrated into the model registry."""
        from ml_core import MODEL_REGISTRY, get_available_models_by_type
        
        # Test registry contains new models
        self.assertIn('saits', MODEL_REGISTRY)
        self.assertIn('brits', MODEL_REGISTRY)
        
        # Test model types
        self.assertEqual(MODEL_REGISTRY['saits']['type'], 'deep_advanced')
        self.assertEqual(MODEL_REGISTRY['brits']['type'], 'deep_advanced')
        
        # Test model classes are available
        self.assertIsNotNone(MODEL_REGISTRY['saits']['model_class'])
        self.assertIsNotNone(MODEL_REGISTRY['brits']['model_class'])
        
        # Test performance tiers
        self.assertEqual(MODEL_REGISTRY['saits']['performance_tier'], 'highest')
        self.assertEqual(MODEL_REGISTRY['brits']['performance_tier'], 'high')
        
        # Test available models by type
        available = get_available_models_by_type()
        advanced_models = available['deep_advanced']
        
        saits_found = any(model['key'] == 'saits' for model in advanced_models)
        brits_found = any(model['key'] == 'brits' for model in advanced_models)
        
        self.assertTrue(saits_found, "SAITS not found in available advanced models")
        self.assertTrue(brits_found, "BRITS not found in available advanced models")

if __name__ == '__main__':
    print("🧪 Running Phase 2 Core Models Test Suite")
    print("="*60)
    
    # Run tests with verbose output
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "="*60)
    print("✅ Phase 2 Core Models Test Suite Completed")
