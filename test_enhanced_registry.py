#!/usr/bin/env python3
"""
Test script for enhanced model registry implementation.
"""

def test_enhanced_registry():
    """Test the enhanced model registry functionality."""
    print("🧪 Testing Enhanced Model Registry...")
    print("=" * 60)
    
    try:
        from ml_core import (
            MODEL_REGISTRY, DEEP_MODELS_AVAILABLE, ADVANCED_MODELS_AVAILABLE,
            check_model_dependencies, get_model_class_safe, get_available_models_by_type,
            recommend_models_for_task
        )
        
        # Test dependency status
        print("\n📋 Testing dependency status check...")
        status = check_model_dependencies()
        print(f"Dependency status: {status}")
        
        # Test model registry contents
        print(f"\n📊 Testing MODEL_REGISTRY contents...")
        print(f"Total models in registry: {len(MODEL_REGISTRY)}")
        
        # List all models by type
        print(f"\n🔍 Models by type:")
        for model_key, config in MODEL_REGISTRY.items():
            model_type = config.get('type', 'shallow')
            available = config['model_class'] is not None
            status_icon = "✅" if available else "❌"
            print(f"   {status_icon} {model_key}: {config['name']} ({model_type})")
        
        # Test safe model class getter
        print(f"\n🔧 Testing safe model class getter...")
        autoencoder_class = get_model_class_safe('autoencoder')
        print(f"Autoencoder class: {autoencoder_class}")
        
        unknown_class = get_model_class_safe('unknown_model')
        print(f"Unknown model class: {unknown_class}")
        
        # Test models by type
        print(f"\n📂 Testing models by type...")
        models_by_type = get_available_models_by_type()
        for model_type, models in models_by_type.items():
            print(f"   {model_type}: {len(models)} models")
            for model in models:
                status_icon = "✅" if model['available'] else "❌"
                print(f"     {status_icon} {model['key']}: {model['name']}")
        
        # Test model recommendations
        print(f"\n🎯 Testing model recommendations...")
        
        # Test balanced recommendations
        balanced_recs = recommend_models_for_task({'balanced': True})
        print(f"Balanced recommendations: {balanced_recs}")
        
        # Test accuracy priority
        accuracy_recs = recommend_models_for_task({'accuracy_priority': True})
        print(f"Accuracy priority recommendations: {accuracy_recs}")
        
        # Test speed priority
        speed_recs = recommend_models_for_task({'speed_priority': True})
        print(f"Speed priority recommendations: {speed_recs}")
        
        print("\n" + "=" * 60)
        print("✅ Enhanced model registry tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced model registry test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_model_configs():
    """Test advanced model configurations if available."""
    print("\n🧪 Testing Advanced Model Configurations...")
    
    try:
        from ml_core import MODEL_REGISTRY, ADVANCED_MODELS_AVAILABLE
        
        if ADVANCED_MODELS_AVAILABLE:
            print("✅ Advanced models are available")
            
            # Test advanced model entries
            advanced_models = ['saits', 'brits', 'enhanced_unet']
            for model_key in advanced_models:
                if model_key in MODEL_REGISTRY:
                    config = MODEL_REGISTRY[model_key]
                    print(f"\n📋 {model_key} configuration:")
                    print(f"   Name: {config['name']}")
                    print(f"   Type: {config['type']}")
                    print(f"   Description: {config.get('description', 'N/A')}")
                    print(f"   Performance tier: {config.get('performance_tier', 'N/A')}")
                    print(f"   Computational cost: {config.get('computational_cost', 'N/A')}")
                    print(f"   Model class: {config['model_class']}")
                    print(f"   Hyperparameters: {len(config.get('hyperparameters', {}))} params")
                else:
                    print(f"❌ {model_key} not found in registry")
        else:
            print("⚠️ Advanced models not available (expected during Phase 1)")
            print("💡 This is normal - advanced models will be implemented in Phase 2")
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced model configuration test failed: {e}")
        return False

if __name__ == "__main__":
    success1 = test_enhanced_registry()
    success2 = test_advanced_model_configs()
    
    if success1 and success2:
        print("\n🎉 ALL ENHANCED REGISTRY TESTS PASSED!")
        print("✅ Enhanced model registry is ready for Phase 2 implementation")
    else:
        print("\n❌ Some tests failed. Review implementation.")
