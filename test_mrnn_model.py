#!/usr/bin/env python3
"""
Comprehensive test suite for mRNN model implementation.
Tests model initialization, training, prediction, and integration with existing workflow.
"""

import unittest
import torch
import numpy as np
import warnings
from typing import Dict, Any

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

class TestMRNNModel(unittest.TestCase):
    """Test suite for mRNN model implementation."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create synthetic test data
        np.random.seed(42)
        torch.manual_seed(42)
        
        self.n_samples = 100
        self.sequence_len = 64
        self.n_features = 4
        
        # Generate synthetic well log data
        depth = np.linspace(0, 1000, self.sequence_len)
        
        # Create realistic well log patterns with multi-scale features
        gr_base = 50 + 30 * np.sin(depth / 100) + 10 * np.sin(depth / 20) + np.random.normal(0, 5, self.sequence_len)
        nphi_base = 0.2 + 0.1 * np.cos(depth / 150) + 0.05 * np.cos(depth / 30) + np.random.normal(0, 0.02, self.sequence_len)
        rhob_base = 2.3 + 0.3 * np.sin(depth / 200) + 0.1 * np.sin(depth / 40) + np.random.normal(0, 0.05, self.sequence_len)
        dt_base = 100 + 50 * np.cos(depth / 120) + 20 * np.cos(depth / 25) + np.random.normal(0, 10, self.sequence_len)
        
        # Create multiple samples with variations
        self.test_data = []
        self.truth_data = []
        
        for i in range(self.n_samples):
            # Add sample-specific variations
            variation = np.random.normal(1, 0.1, self.sequence_len)
            
            sample = np.column_stack([
                gr_base * variation,
                nphi_base * variation,
                rhob_base * variation,
                dt_base * variation
            ])
            
            # Create truth data (complete)
            truth_sample = sample.copy()
            
            # Create missing data (introduce NaN values)
            missing_mask = np.random.random((self.sequence_len, self.n_features)) < 0.2
            sample[missing_mask] = np.nan
            
            self.test_data.append(sample)
            self.truth_data.append(truth_sample)
        
        self.test_data = np.array(self.test_data)
        self.truth_data = np.array(self.truth_data)
        
        # Convert to tensors
        self.test_tensor = torch.tensor(self.test_data, dtype=torch.float32)
        self.truth_tensor = torch.tensor(self.truth_data, dtype=torch.float32)
        
        print(f"🧪 Test data created: {self.test_tensor.shape}")
        print(f"   Missing values: {torch.isnan(self.test_tensor).sum().item()}")
    
    def test_model_initialization(self):
        """Test mRNN model initialization."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            # Test with default parameters
            model = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                epochs=2  # Quick test
            )
            
            self.assertIsNotNone(model)
            self.assertEqual(model.n_features, self.n_features)
            self.assertEqual(model.sequence_len, self.sequence_len)
            self.assertFalse(model.is_fitted)
            
            print("✅ mRNN model initialization test passed")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_model_parameter_validation(self):
        """Test parameter validation."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            # Test invalid hidden sizes
            with self.assertRaises(ValueError):
                MRNNModel(
                    n_features=self.n_features,
                    sequence_len=self.sequence_len,
                    hidden_sizes=[]  # Empty list
                )
            
            # Test invalid n_features
            with self.assertRaises(ValueError):
                MRNNModel(
                    n_features=1,  # Too few features
                    sequence_len=self.sequence_len
                )
            
            print("✅ Parameter validation test passed")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_model_training(self):
        """Test model training workflow."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            model = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=[32, 64, 128],  # Smaller for faster testing
                n_layers=2,
                epochs=3,  # Quick test
                batch_size=16
            )
            
            # Test training
            model.fit(self.test_tensor, self.truth_tensor)
            
            self.assertTrue(model.is_fitted)
            self.assertIsNotNone(model.model)
            self.assertGreater(len(model.training_history['loss']), 0)
            
            print("✅ Model training test passed")
            print(f"   Final loss: {model.training_history['loss'][-1]:.6f}")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_model_prediction(self):
        """Test model prediction workflow."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            model = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=[32, 64],
                n_layers=2,
                epochs=3,
                batch_size=16
            )
            
            # Train model
            model.fit(self.test_tensor, self.truth_tensor)
            
            # Test prediction
            predictions = model.predict(self.test_tensor)
            
            self.assertIsInstance(predictions, torch.Tensor)
            self.assertEqual(predictions.shape, self.test_tensor.shape)
            
            # Check that NaN values are imputed
            original_nan_count = torch.isnan(self.test_tensor).sum().item()
            predicted_nan_count = torch.isnan(predictions).sum().item()
            
            self.assertLessEqual(predicted_nan_count, original_nan_count)
            
            print("✅ Model prediction test passed")
            print(f"   Original NaN count: {original_nan_count}")
            print(f"   Predicted NaN count: {predicted_nan_count}")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_multi_resolution_architecture(self):
        """Test multi-resolution architecture components."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel, MRNNNet
            
            # Test MRNNNet directly
            net = MRNNNet(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=[32, 64],
                n_layers=2,
                bidirectional=True,
                attention_dim=64
            )
            
            # Test forward pass
            dummy_input = torch.randn(5, self.sequence_len, self.n_features)
            output = net(dummy_input)
            
            self.assertEqual(output.shape, dummy_input.shape)
            
            print("✅ Multi-resolution architecture test passed")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_attention_mechanism(self):
        """Test attention mechanism."""
        try:
            from models.advanced_models.mrnn_model import AttentionMechanism
            
            attention = AttentionMechanism(input_dim=128, attention_dim=64)
            
            # Test attention forward pass
            dummy_features = torch.randn(5, self.sequence_len, 128)
            attended_features, attention_weights = attention(dummy_features)
            
            self.assertEqual(attended_features.shape, (5, 128))
            self.assertEqual(attention_weights.shape, (5, self.sequence_len))
            
            # Check attention weights sum to 1
            weights_sum = torch.sum(attention_weights, dim=1)
            self.assertTrue(torch.allclose(weights_sum, torch.ones(5), atol=1e-6))
            
            print("✅ Attention mechanism test passed")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_model_complexity_calculation(self):
        """Test model complexity calculation."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            model = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=[64, 128, 256],
                n_layers=3,
                bidirectional=True,
                attention_dim=128
            )
            
            complexity = model.get_model_complexity()
            
            self.assertIsInstance(complexity, dict)
            self.assertIn('total_parameters', complexity)
            self.assertIn('hidden_sizes', complexity)
            self.assertIn('resolution_levels', complexity)
            self.assertIn('complexity_score', complexity)
            
            self.assertGreater(complexity['total_parameters'], 0)
            self.assertEqual(complexity['hidden_sizes'], [64, 128, 256])
            self.assertEqual(complexity['resolution_levels'], 3)
            
            print("✅ Model complexity calculation test passed")
            print(f"   Total parameters: {complexity['total_parameters']:,}")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_device_handling(self):
        """Test device handling (CPU/GPU)."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            model = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=[32, 64],
                epochs=1
            )
            
            # Check device assignment
            expected_device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.assertEqual(model.device.type, expected_device.type)
            
            print(f"✅ Device handling test passed (Device: {model.device})")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_error_handling(self):
        """Test error handling for invalid inputs."""
        try:
            from models.advanced_models.mrnn_model import MRNNModel
            
            model = MRNNModel(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                epochs=1
            )
            
            # Test prediction without training
            with self.assertRaises(RuntimeError):
                model.predict(self.test_tensor)
            
            # Test invalid input shapes
            invalid_tensor = torch.randn(10, 32, 3)  # Wrong number of features
            
            # Train with valid data first
            model.fit(self.test_tensor[:10], self.truth_tensor[:10])
            
            # This should handle the shape mismatch gracefully
            try:
                model.predict(invalid_tensor)
            except (ValueError, RuntimeError):
                pass  # Expected behavior
            
            print("✅ Error handling test passed")
            
        except ImportError:
            self.skipTest("mRNN model not available")
    
    def test_model_registry_integration(self):
        """Test integration with model registry."""
        try:
            from ml_core import MODEL_REGISTRY
            from models.advanced_models import ADVANCED_MODELS_STATUS
            
            # Check if mrnn is in registry
            if ADVANCED_MODELS_STATUS.get('mrnn', False):
                self.assertIn('mrnn', MODEL_REGISTRY)
                
                config = MODEL_REGISTRY['mrnn']
                self.assertEqual(config['type'], 'deep_advanced')
                self.assertIsNotNone(config['model_class'])
                self.assertIn('hyperparameters', config)
                
                print("✅ Model registry integration test passed")
            else:
                self.skipTest("mRNN model not available in registry")
                
        except ImportError:
            self.skipTest("Model registry not available")
    
    def test_workflow_compatibility(self):
        """Test compatibility with existing workflow."""
        try:
            from ml_core import MODEL_REGISTRY
            from models.advanced_models import ADVANCED_MODELS_STATUS
            
            if not ADVANCED_MODELS_STATUS.get('mrnn', False):
                self.skipTest("mRNN model not available")
            
            # Get model configuration
            model_config = MODEL_REGISTRY['mrnn']
            model_class = model_config['model_class']
            
            # Create hyperparameters
            hparams = {k: v['default'] for k, v in model_config['hyperparameters'].items()}
            hparams['epochs'] = 2  # Quick test
            
            # Test model creation and training
            model = model_class(**hparams)
            model.fit(self.test_tensor[:20], self.truth_tensor[:20])
            
            # Test prediction
            predictions = model.predict(self.test_tensor[:5])
            
            self.assertIsInstance(predictions, torch.Tensor)
            self.assertEqual(predictions.shape, self.test_tensor[:5].shape)
            
            print("✅ Workflow compatibility test passed")
            
        except ImportError:
            self.skipTest("Workflow components not available")

def run_mrnn_tests():
    """Run all mRNN model tests."""
    print("🧪 Running mRNN Model Tests")
    print("=" * 50)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMRNNModel)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All mRNN model tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        for test, traceback in result.failures + result.errors:
            print(f"\n❌ {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_mrnn_tests()
