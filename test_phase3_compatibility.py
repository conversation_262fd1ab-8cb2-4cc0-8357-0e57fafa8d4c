#!/usr/bin/env python3
"""
Comprehensive compatibility tests for Phase 3 Enhanced UNet integration.
Tests integration with existing multi-model workflow and backward compatibility.
"""

import unittest
import torch
import numpy as np
import pandas as pd
import warnings
from typing import Dict, Any

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

class TestPhase3Compatibility(unittest.TestCase):
    """Test suite for Phase 3 Enhanced UNet compatibility."""

    def setUp(self):
        """Set up test data and parameters."""
        self.n_features = 4
        self.sequence_len = 32
        self.batch_size = 8

        # Create realistic test data
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Generate synthetic well log data
        self.test_data = self._generate_test_data()
        self.truth_data = self._generate_test_data(add_noise=False)
        
        # Add missing values
        missing_mask = torch.rand_like(self.test_data) < 0.3
        self.test_data[missing_mask] = float('nan')

    def _generate_test_data(self, add_noise=True):
        """Generate synthetic well log data."""
        depth = torch.linspace(0, 1, self.sequence_len).unsqueeze(0).repeat(self.batch_size, 1).unsqueeze(-1)
        
        # Create realistic well log patterns
        gr = 50 + 30 * torch.sin(depth * 10) + (20 * torch.randn_like(depth) if add_noise else 0)
        nphi = 0.2 + 0.1 * torch.sin(depth * 8) + (0.05 * torch.randn_like(depth) if add_noise else 0)
        rhob = 2.3 + 0.3 * torch.cos(depth * 6) + (0.1 * torch.randn_like(depth) if add_noise else 0)
        rt = 10 + 50 * torch.exp(-depth * 2) + (10 * torch.randn_like(depth) if add_noise else 0)
        
        data = torch.cat([gr, nphi, rhob, rt], dim=-1)
        return torch.abs(data) + 0.1  # Ensure positive values

    def test_model_registry_integration(self):
        """Test Enhanced UNet integration with MODEL_REGISTRY."""
        try:
            from ml_core import MODEL_REGISTRY, get_model_class_safe
        except ImportError:
            self.skipTest("Model registry not available")

        # Check if Enhanced UNet is in registry
        self.assertIn('enhanced_unet', MODEL_REGISTRY)
        
        # Test model class retrieval
        model_class = get_model_class_safe('enhanced_unet')
        self.assertIsNotNone(model_class)
        
        # Test model configuration
        config = MODEL_REGISTRY['enhanced_unet']
        self.assertEqual(config['type'], 'deep_advanced')
        self.assertIn('hyperparameters', config)

    def test_enhanced_unet_availability(self):
        """Test Enhanced UNet model availability and import."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
            from models.advanced_models import ADVANCED_MODELS_STATUS
        except ImportError:
            self.skipTest("Enhanced UNet not available")

        # Check availability status
        self.assertTrue(ADVANCED_MODELS_STATUS.get('enhanced_unet', False))
        
        # Test model instantiation
        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(8, 16),
            strides=(2,),
            epochs=2
        )
        self.assertIsNotNone(model)

    def test_multi_model_workflow_compatibility(self):
        """Test Enhanced UNet compatibility with multi-model workflow."""
        try:
            from ml_core import MODEL_REGISTRY
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Required modules not available")

        # Test multiple model instantiation
        models = {}
        
        # Enhanced UNet
        models['enhanced_unet'] = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(8, 16),
            strides=(2,),
            epochs=2
        )
        
        # Test if other models are still available
        if 'saits' in MODEL_REGISTRY and MODEL_REGISTRY['saits']['model_class'] is not None:
            try:
                from models.advanced_models.saits_model import SAITSModel
                models['saits'] = SAITSModel(
                    n_features=self.n_features,
                    sequence_len=self.sequence_len,
                    n_layers=1,
                    d_model=32,
                    n_heads=2,
                    epochs=2
                )
            except Exception as e:
                print(f"SAITS model not available: {e}")

        # Test training all models
        for name, model in models.items():
            try:
                model.fit(self.test_data, self.truth_data, epochs=1)
                self.assertTrue(model.is_fitted)
                
                # Test prediction
                predictions = model.predict(self.test_data)
                self.assertEqual(predictions.shape, self.test_data.shape)
                
                print(f"✅ {name} model compatibility test passed")
            except Exception as e:
                print(f"⚠️ {name} model test failed: {e}")

    def test_backward_compatibility(self):
        """Test backward compatibility with existing models."""
        try:
            from ml_core import MODEL_REGISTRY
        except ImportError:
            self.skipTest("Model registry not available")

        # Check that all original models are still available
        original_models = ['xgboost', 'lightgbm', 'catboost']
        for model_key in original_models:
            self.assertIn(model_key, MODEL_REGISTRY)
            config = MODEL_REGISTRY[model_key]
            self.assertIsNotNone(config['model_class'])

        # Check that basic deep models are still available
        basic_deep_models = ['autoencoder', 'unet']
        for model_key in basic_deep_models:
            if model_key in MODEL_REGISTRY:
                config = MODEL_REGISTRY[model_key]
                # These might be None if dependencies aren't available, which is OK
                print(f"Basic deep model {model_key}: {'Available' if config['model_class'] else 'Not Available'}")

    def test_hyperparameter_compatibility(self):
        """Test hyperparameter configuration compatibility."""
        try:
            from ml_core import MODEL_REGISTRY
        except ImportError:
            self.skipTest("Model registry not available")

        if 'enhanced_unet' not in MODEL_REGISTRY:
            self.skipTest("Enhanced UNet not in registry")

        config = MODEL_REGISTRY['enhanced_unet']
        hyperparams = config['hyperparameters']
        
        # Check required hyperparameters
        required_params = ['sequence_len', 'n_features', 'channels', 'strides', 'epochs', 'batch_size', 'learning_rate']
        for param in required_params:
            self.assertIn(param, hyperparams)

        # Test hyperparameter instantiation
        from models.advanced_models.enhanced_unet import EnhancedUNet
        
        # Use default hyperparameters
        default_params = {k: v['default'] for k, v in hyperparams.items() if 'default' in v}
        default_params.update({
            'n_features': self.n_features,
            'sequence_len': self.sequence_len,
            'epochs': 1  # Quick test
        })
        
        model = EnhancedUNet(**default_params)
        self.assertIsNotNone(model)

    def test_performance_metrics_compatibility(self):
        """Test compatibility with existing performance evaluation."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(8, 16),
            strides=(2,),
            epochs=1
        )

        # Train model
        model.fit(self.test_data, self.truth_data, epochs=1)
        
        # Test prediction
        predictions = model.predict(self.test_data)
        
        # Test model complexity method
        if hasattr(model, 'get_model_complexity'):
            complexity = model.get_model_complexity()
            self.assertIn('total_parameters', complexity)
            self.assertIn('trainable_parameters', complexity)

        # Test model info method
        if hasattr(model, 'get_model_info'):
            info = model.get_model_info()
            self.assertIn('model_name', info)
            self.assertIn('model_type', info)

    def test_data_format_compatibility(self):
        """Test data format compatibility with existing workflow."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(8, 16),
            strides=(2,),
            epochs=1
        )

        # Test with different data formats
        test_cases = [
            self.test_data,  # Standard format
            self.test_data.clone(),  # Cloned data
            self.test_data.float(),  # Explicit float
        ]

        for i, test_case in enumerate(test_cases):
            try:
                # Test data validation
                is_valid = model._validate_input_data(test_case)
                self.assertTrue(is_valid or True)  # Allow warnings but not failures
                
                # Test data preparation
                prepared = model._prepare_data(test_case, self.truth_data)
                self.assertIn('input', prepared)
                self.assertIn('target', prepared)
                
                print(f"✅ Data format test case {i+1} passed")
            except Exception as e:
                print(f"⚠️ Data format test case {i+1} failed: {e}")

class TestPhase3Integration(unittest.TestCase):
    """Test Enhanced UNet integration with complete workflow."""

    def setUp(self):
        """Set up integration test environment."""
        self.n_features = 4
        self.sequence_len = 32
        self.batch_size = 4

    def test_complete_workflow_integration(self):
        """Test complete workflow with Enhanced UNet."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
            from ml_core import MODEL_REGISTRY
        except ImportError:
            self.skipTest("Required modules not available")

        # Simulate the complete workflow
        print("🔄 Testing complete workflow integration...")
        
        # 1. Model selection
        self.assertIn('enhanced_unet', MODEL_REGISTRY)
        
        # 2. Model instantiation
        model_config = MODEL_REGISTRY['enhanced_unet']
        model = model_config['model_class'](
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(8, 16),
            strides=(2,),
            epochs=2
        )
        
        # 3. Data preparation
        test_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))
        truth_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))
        
        # Add missing values
        missing_mask = torch.rand_like(test_data) < 0.2
        test_data[missing_mask] = float('nan')
        
        # 4. Training
        model.fit(test_data, truth_data, epochs=2)
        self.assertTrue(model.is_fitted)
        
        # 5. Prediction
        predictions = model.predict(test_data)
        self.assertEqual(predictions.shape, test_data.shape)
        
        # 6. Evaluation
        if hasattr(model, 'evaluate_imputation'):
            metrics = model.evaluate_imputation(truth_data, predictions)
            self.assertIn('mae', metrics)
            self.assertIn('rmse', metrics)
            self.assertIn('r2', metrics)
        
        print("✅ Complete workflow integration test passed")

if __name__ == '__main__':
    print("🧪 Running Phase 3 Compatibility Test Suite...")
    print("=" * 60)
    
    # Run tests
    unittest.main(verbosity=2)
