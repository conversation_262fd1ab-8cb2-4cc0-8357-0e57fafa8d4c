#!/usr/bin/env python3
"""
Integration tests for Phase 2 implementation.
Tests backward compatibility and integration with existing workflow.
"""

import unittest
import torch
import numpy as np
import pandas as pd
import warnings
from typing import Dict, Any

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

class TestPhase2Integration(unittest.TestCase):
    """Integration tests for Phase 2 implementation."""
    
    def setUp(self):
        """Set up test data and parameters."""
        # Create synthetic well log DataFrame
        np.random.seed(42)
        
        n_samples = 200
        wells = ['WELL_A', 'WELL_B']
        
        data = []
        for well in wells:
            for i in range(n_samples // len(wells)):
                depth = 1000 + i * 0.5
                gr = 50 + 30 * np.sin(i * 0.1) + np.random.normal(0, 5)
                nphi = 0.2 + 0.1 * np.cos(i * 0.08) + np.random.normal(0, 0.02)
                rhob = 2.3 + 0.3 * np.sin(i * 0.12) + np.random.normal(0, 0.1)
                
                # Add some missing values
                if np.random.random() < 0.1:  # 10% missing
                    rhob = np.nan
                
                data.append({
                    'WELL': well,
                    'DEPTH': depth,
                    'GR': max(0, gr),
                    'NPHI': max(0, min(1, nphi)),
                    'RHOB': rhob if not np.isnan(rhob) else np.nan
                })
        
        self.test_df = pd.DataFrame(data)
        self.feature_cols = ['GR', 'NPHI']
        self.target_col = 'RHOB'
        
        print(f"📊 Test DataFrame shape: {self.test_df.shape}")
        print(f"🔍 Missing values in {self.target_col}: {self.test_df[self.target_col].isna().sum()}")
    
    def test_backward_compatibility_simple_models(self):
        """Test that existing simple models still work unchanged."""
        try:
            from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet
            
            # Test SimpleAutoencoder
            autoencoder = SimpleAutoencoder(
                n_features=3,
                sequence_len=32,
                epochs=2
            )
            self.assertIsNotNone(autoencoder)
            
            # Test SimpleUNet
            unet = SimpleUNet(
                n_features=3,
                sequence_len=32,
                epochs=2
            )
            self.assertIsNotNone(unet)
            
            print("✅ Simple models backward compatibility verified")
            
        except ImportError as e:
            self.fail(f"Simple models not available: {e}")
    
    def test_model_registry_backward_compatibility(self):
        """Test that model registry maintains backward compatibility."""
        from ml_core import MODEL_REGISTRY
        
        # Check that original models are still present
        original_models = ['xgboost', 'lightgbm', 'catboost', 'autoencoder', 'unet']
        
        for model_key in original_models:
            self.assertIn(model_key, MODEL_REGISTRY, f"Original model {model_key} missing from registry")
            
            # Check that model has required fields
            config = MODEL_REGISTRY[model_key]
            self.assertIn('name', config)
            self.assertIn('model_class', config)
            self.assertIn('hyperparameters', config)
        
        print("✅ Model registry backward compatibility verified")
    
    def test_advanced_models_integration(self):
        """Test that advanced models integrate properly with existing workflow."""
        from ml_core import MODEL_REGISTRY, get_model_class_safe
        
        # Test SAITS integration
        if 'saits' in MODEL_REGISTRY:
            saits_class = get_model_class_safe('saits')
            self.assertIsNotNone(saits_class, "SAITS model class should be available")
            
            # Test model instantiation with registry parameters
            saits_config = MODEL_REGISTRY['saits']['hyperparameters']
            saits_model = saits_class(
                n_features=3,
                sequence_len=32,
                n_layers=1,
                d_model=64,
                n_heads=2,
                epochs=2
            )
            self.assertIsNotNone(saits_model)
        
        # Test BRITS integration
        if 'brits' in MODEL_REGISTRY:
            brits_class = get_model_class_safe('brits')
            self.assertIsNotNone(brits_class, "BRITS model class should be available")
            
            # Test model instantiation with registry parameters
            brits_config = MODEL_REGISTRY['brits']['hyperparameters']
            brits_model = brits_class(
                n_features=3,
                sequence_len=32,
                rnn_hidden_size=64,
                epochs=2
            )
            self.assertIsNotNone(brits_model)
        
        print("✅ Advanced models integration verified")
    
    def test_model_recommendation_system(self):
        """Test that model recommendation system works with new models."""
        from ml_core import recommend_models_for_task, get_models_by_performance_tier
        
        # Test accuracy-focused recommendations
        accuracy_models = recommend_models_for_task({'accuracy_priority': True})
        self.assertIsInstance(accuracy_models, list)
        self.assertGreater(len(accuracy_models), 0)
        
        # Test speed-focused recommendations
        speed_models = recommend_models_for_task({'speed_priority': True})
        self.assertIsInstance(speed_models, list)
        self.assertGreater(len(speed_models), 0)
        
        # Test balanced recommendations
        balanced_models = recommend_models_for_task({'balanced': True})
        self.assertIsInstance(balanced_models, list)
        self.assertGreater(len(balanced_models), 0)
        
        # Test performance tier filtering
        highest_tier = get_models_by_performance_tier('highest')
        high_tier = get_models_by_performance_tier('high')
        
        self.assertIsInstance(highest_tier, list)
        self.assertIsInstance(high_tier, list)
        
        print("✅ Model recommendation system verified")
    
    def test_data_handler_compatibility(self):
        """Test that data handling functions work with new models."""
        try:
            from data_handler import normalize_data, create_sequences, introduce_missingness
            
            # Test data normalization
            all_features = self.feature_cols + [self.target_col]
            normalized_df, scalers = normalize_data(self.test_df, all_features)
            
            self.assertIsNotNone(normalized_df)
            self.assertIsNotNone(scalers)
            self.assertEqual(normalized_df.shape, self.test_df.shape)
            
            # Test sequence creation
            sequences, metadata = create_sequences(
                normalized_df, 'WELL', all_features, sequence_len=32
            )
            
            self.assertIsNotNone(sequences)
            self.assertIsNotNone(metadata)
            self.assertEqual(sequences.shape[2], len(all_features))
            
            # Test missingness introduction
            if sequences.shape[0] > 0:
                missing_sequences = introduce_missingness(
                    sequences, target_col_name=self.target_col,
                    feature_names=all_features, missing_rate=0.2
                )
                
                self.assertIsNotNone(missing_sequences)
                self.assertEqual(missing_sequences.shape, sequences.shape)
            
            print("✅ Data handler compatibility verified")
            
        except ImportError as e:
            self.fail(f"Data handler functions not available: {e}")
    
    def test_error_handling_and_fallbacks(self):
        """Test error handling and fallback mechanisms."""
        from ml_core import get_model_class_safe, check_model_dependencies
        
        # Test safe model class retrieval
        valid_model = get_model_class_safe('xgboost')
        self.assertIsNotNone(valid_model)
        
        invalid_model = get_model_class_safe('nonexistent_model')
        self.assertIsNone(invalid_model)
        
        # Test dependency checking
        deps_status = check_model_dependencies()
        self.assertIsInstance(deps_status, dict)
        self.assertIn('basic_models', deps_status)
        self.assertIn('advanced_models', deps_status)
        
        print("✅ Error handling and fallbacks verified")
    
    def test_performance_monitoring(self):
        """Test performance monitoring and metrics collection."""
        from ml_core import create_model_performance_summary
        
        # Test performance summary creation
        summary = create_model_performance_summary()
        
        self.assertIsInstance(summary, dict)
        self.assertIn('total_models', summary)
        self.assertIn('available_models', summary)
        self.assertIn('by_type', summary)
        self.assertIn('by_performance_tier', summary)
        self.assertIn('recommendations', summary)
        
        # Verify summary structure
        self.assertGreater(summary['total_models'], 0)
        self.assertGreaterEqual(summary['available_models'], 0)
        
        print("✅ Performance monitoring verified")
    
    def test_memory_and_resource_management(self):
        """Test memory usage and resource management."""
        from models.advanced_models.saits_model import SAITSModel
        from models.advanced_models.brits_model import BRITSModel
        
        # Test SAITS memory estimation
        saits = SAITSModel(n_features=4, sequence_len=32, d_model=64, n_heads=2)
        saits_complexity = saits.get_model_complexity()
        
        self.assertIn('total_parameters', saits_complexity)
        self.assertIn('memory_mb', saits_complexity)
        self.assertGreater(saits_complexity['total_parameters'], 0)
        self.assertGreater(saits_complexity['memory_mb'], 0)
        
        # Test BRITS memory estimation
        brits = BRITSModel(n_features=4, sequence_len=32, rnn_hidden_size=64)
        brits_complexity = brits.get_model_complexity()
        
        self.assertIn('total_parameters', brits_complexity)
        self.assertIn('memory_mb', brits_complexity)
        self.assertGreater(brits_complexity['total_parameters'], 0)
        self.assertGreater(brits_complexity['memory_mb'], 0)
        
        print("✅ Memory and resource management verified")

if __name__ == '__main__':
    print("🔗 Running Phase 2 Integration Test Suite")
    print("="*60)
    
    # Run tests with verbose output
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "="*60)
    print("✅ Phase 2 Integration Test Suite Completed")
