#!/usr/bin/env python3
"""
Test script to reproduce and fix the SAITS DataFrame/Tensor issue
"""

import numpy as np
import pandas as pd
import torch
from data_handler import create_sequences, introduce_missingness

def test_data_pipeline():
    """Test the data processing pipeline that was causing issues"""
    print("🔍 Testing data processing pipeline...")
    
    # Create mock well log data similar to what main.py would have
    # Make data more realistic with larger continuous sections
    n_rows_per_well = 300
    np.random.seed(42)
    
    df = pd.DataFrame({
        'WELL': ['Well_A'] * n_rows_per_well + ['Well_B'] * n_rows_per_well,
        'MD': np.concatenate([np.linspace(1000, 1600, n_rows_per_well), 
                             np.linspace(2000, 2600, n_rows_per_well)]),
        'GR': np.random.normal(50, 20, n_rows_per_well * 2),
        'NPHI': np.random.normal(0.2, 0.1, n_rows_per_well * 2),
        'RHOB': np.random.normal(2.3, 0.3, n_rows_per_well * 2),
        'P-WAVE': np.random.normal(200, 50, n_rows_per_well * 2)
    })
    
    # Add some missing values to make it realistic, but keep most data intact
    # Create missing sections rather than random missing points
    for well in ['Well_A', 'Well_B']:
        well_mask = df['WELL'] == well
        well_indices = df[well_mask].index
        
        # Add a few missing sections of 5-10 points each
        for i in range(3):  # 3 missing sections per well
            start_idx = np.random.choice(well_indices[10:-10])  # Avoid edges
            end_idx = start_idx + np.random.randint(5, 10)
            df.loc[start_idx:end_idx, 'P-WAVE'] = np.nan
    
    feature_cols = ['GR', 'NPHI', 'RHOB', 'P-WAVE']
    sequence_len = 32  # Reduced sequence length
    
    print(f"Mock data shape: {df.shape}")
    print(f"Feature columns: {feature_cols}")
    print(f"Missing values in P-WAVE: {df['P-WAVE'].isna().sum()}")
    print(f"Sequence length: {sequence_len}")
    
    # Test create_sequences with enhanced=True
    print("\n🔧 Testing create_sequences with enhanced preprocessing...")
    try:
        sequences, metadata = create_sequences(df, 'WELL', feature_cols, 
                                             sequence_len=sequence_len, 
                                             use_enhanced=True)
        print(f"✅ Sequences created successfully!")
        print(f"   Type: {type(sequences)}")
        print(f"   Shape: {sequences.shape}")
        print(f"   Dtype: {sequences.dtype}")
        print(f"   Metadata length: {len(metadata)}")
        
        if len(sequences) == 0:
            print("⚠️ No sequences created, trying without enhanced preprocessing...")
            sequences, metadata = create_sequences(df, 'WELL', feature_cols, 
                                                 sequence_len=sequence_len, 
                                                 use_enhanced=False)
            print(f"   Standard sequences shape: {sequences.shape}")
            
    except Exception as e:
        print(f"❌ Error in create_sequences: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    if len(sequences) == 0:
        print("❌ No sequences could be created")
        return False
    
    # Test introduce_missingness
    print("\n🔧 Testing introduce_missingness...")
    try:
        sequences_missing = introduce_missingness(sequences, missing_rate=0.3, 
                                                use_enhanced=True,
                                                target_col_name='P-WAVE',
                                                feature_names=feature_cols)
        print(f"✅ Missing values introduced successfully!")
        print(f"   Type: {type(sequences_missing)}")
        print(f"   Shape: {sequences_missing.shape}")
        print(f"   Dtype: {sequences_missing.dtype}")
        print(f"   Missing values: {np.isnan(sequences_missing).sum()}")
        
    except Exception as e:
        print(f"❌ Error in introduce_missingness: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test tensor conversion
    print("\n🔧 Testing tensor conversion...")
    try:
        train_tensor = torch.from_numpy(sequences_missing.astype(np.float32))
        truth_tensor = torch.from_numpy(sequences.astype(np.float32))
        
        print(f"✅ Tensor conversion successful!")
        print(f"   Train tensor: {train_tensor.shape}, {train_tensor.dtype}")
        print(f"   Truth tensor: {truth_tensor.shape}, {truth_tensor.dtype}")
        
    except Exception as e:
        print(f"❌ Error in tensor conversion: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_data_pipeline()
    if success:
        print("\n✅ Data pipeline test passed! The SAITS error should be fixed.")
    else:
        print("\n❌ Data pipeline test failed! Need further investigation.")
