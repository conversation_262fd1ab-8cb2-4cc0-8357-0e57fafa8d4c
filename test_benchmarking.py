#!/usr/bin/env python3
"""
Test script for the performance benchmarking framework.
"""

import sys
import os
sys.path.append('tests')

from performance_benchmarking import ModelBenchmark
from models.advanced_models.enhanced_unet import EnhancedUNet

def test_benchmarking_framework():
    """Test the benchmarking framework with Enhanced UNet."""
    print("🧪 Testing Performance Benchmarking Framework")
    print("=" * 60)
    
    # Initialize benchmark with small data sizes for testing
    benchmark = ModelBenchmark(test_data_sizes=[
        (8, 32, 4),   # Very small for quick testing
        (16, 32, 4),  # Small
    ])
    
    # Define models to test
    models_to_test = {
        'enhanced_unet': (
            EnhancedUNet,
            {
                'channels': (8, 16),  # Small for testing
                'strides': (2,),
                'learning_rate': 1e-3,
                'epochs': 2  # Quick training
            }
        )
    }
    
    try:
        # Run benchmark
        print("🚀 Running benchmark...")
        results = benchmark.run_comprehensive_benchmark(models_to_test)
        
        # Print summary
        print("\n📊 Benchmark Results:")
        benchmark.print_summary_report()
        
        # Test visualization (without showing plots)
        print("\n📈 Testing visualization generation...")
        benchmark.create_benchmark_visualizations()
        
        # Test export
        print("\n💾 Testing results export...")
        benchmark.export_results('benchmark_results_test.csv')
        
        print("\n✅ Benchmarking framework test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Benchmarking test failed: {e}")
        return False

if __name__ == '__main__':
    success = test_benchmarking_framework()
    if success:
        print("\n🎉 All benchmarking tests passed!")
    else:
        print("\n💥 Benchmarking tests failed!")
        sys.exit(1)
