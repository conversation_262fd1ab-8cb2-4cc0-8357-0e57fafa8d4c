#!/usr/bin/env python3
"""
Comprehensive test suite for Enhanced UNet implementation.
Tests model initialization, training, prediction, and integration with existing workflow.
"""

import unittest
import torch
import numpy as np
import warnings
from typing import Dict, Any

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

class TestEnhancedUNet(unittest.TestCase):
    """Test suite for Enhanced UNet implementation."""

    def setUp(self):
        """Set up test data."""
        self.n_features = 4
        self.sequence_len = 64
        self.batch_size = 8

        # Create test data
        self.test_data = torch.randn(self.batch_size, self.sequence_len, self.n_features)
        self.test_data = torch.abs(self.test_data)  # Well logs are typically positive
        self.test_data[self.test_data > 2.0] = float('nan')  # Add missing values
        self.truth_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))

    def test_model_initialization(self):
        """Test Enhanced UNet model initialization."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32, 64),  # Small for testing
            strides=(2, 2),
            epochs=2,
            batch_size=self.batch_size
        )

        self.assertEqual(model.n_features, self.n_features)
        self.assertEqual(model.sequence_len, self.sequence_len)
        self.assertFalse(model.is_fitted)

    def test_parameter_validation(self):
        """Test parameter validation."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        # Test invalid channels/strides combination
        with self.assertRaises(ValueError):
            EnhancedUNet(channels=(32, 64), strides=(2, 2, 2))  # Mismatched lengths

    def test_training_and_prediction(self):
        """Test model training and prediction."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),  # Small for testing
            strides=(2,),
            epochs=2,
            batch_size=self.batch_size
        )

        # Test training
        model.fit(self.test_data, self.truth_data, epochs=2)
        self.assertTrue(model.is_fitted)

        # Test prediction
        predictions = model.predict(self.test_data)
        self.assertEqual(predictions.shape, self.test_data.shape)
        self.assertFalse(torch.isnan(predictions).all())

    def test_model_complexity(self):
        """Test model complexity calculation."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),
            strides=(2,),
            epochs=2
        )

        complexity = model.get_model_complexity()
        self.assertIn('total_parameters', complexity)
        self.assertIn('trainable_parameters', complexity)
        self.assertIn('channels', complexity)
        self.assertIn('strides', complexity)
        self.assertGreater(complexity['total_parameters'], 0)

    def test_data_preparation(self):
        """Test data preparation methods."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),
            strides=(2,),
            epochs=2
        )

        # Test data preparation
        prepared_data = model._prepare_data(self.test_data, self.truth_data)
        self.assertIn('input', prepared_data)
        self.assertIn('target', prepared_data)
        
        # Check dimensions (should add channel dimension)
        self.assertEqual(len(prepared_data['input'].shape), 4)  # batch, channel, seq, features
        self.assertEqual(prepared_data['input'].shape[1], 1)  # Single channel

    def test_device_handling(self):
        """Test device handling (CPU/GPU)."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),
            strides=(2,),
            epochs=2
        )

        # Initialize model to check device
        model._initialize_model()
        
        # Check that device is set correctly
        self.assertIsNotNone(model.device)
        self.assertTrue(str(model.device) in ['cpu', 'cuda:0'])

    def test_error_handling(self):
        """Test error handling for invalid inputs."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),
            strides=(2,),
            epochs=2
        )

        # Test prediction before fitting
        with self.assertRaises(RuntimeError):
            model.predict(self.test_data)

        # Test invalid data shapes
        invalid_data = torch.randn(5, 10)  # 2D instead of 3D
        self.assertFalse(model._validate_input_data(invalid_data))

class TestEnhancedUNetIntegration(unittest.TestCase):
    """Test Enhanced UNet integration with existing workflow."""

    def setUp(self):
        """Set up integration test data."""
        self.n_features = 4
        self.sequence_len = 32
        self.batch_size = 4

        # Create minimal test data
        self.test_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))
        self.truth_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))
        
        # Add some missing values
        missing_mask = torch.rand_like(self.test_data) < 0.2
        self.test_data[missing_mask] = float('nan')

    def test_model_registry_integration(self):
        """Test integration with model registry."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
            from ml_core import MODEL_REGISTRY
        except ImportError:
            self.skipTest("Enhanced UNet or model registry not available")

        # Check if Enhanced UNet can be added to registry
        test_config = {
            'name': 'Enhanced U-Net Test',
            'model_class': EnhancedUNet,
            'type': 'deep_advanced',
            'hyperparameters': {
                'sequence_len': {'type': int, 'default': 64},
                'n_features': {'type': int, 'default': 4},
                'channels': {'type': list, 'default': [32, 64, 128]},
                'strides': {'type': list, 'default': [2, 2]},
                'epochs': {'type': int, 'default': 50},
                'batch_size': {'type': int, 'default': 32},
                'learning_rate': {'type': float, 'default': 1e-4},
            }
        }

        # Test model instantiation from config
        model = test_config['model_class'](
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),
            strides=(2,),
            epochs=2
        )
        
        self.assertIsInstance(model, EnhancedUNet)

    def test_workflow_compatibility(self):
        """Test compatibility with existing deep learning workflow."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available")

        # Test the complete workflow: init -> fit -> predict
        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(8, 16),  # Very small for fast testing
            strides=(2,),
            epochs=1,  # Single epoch for speed
            batch_size=self.batch_size
        )

        # Training phase
        model.fit(self.test_data, self.truth_data, epochs=1)
        self.assertTrue(model.is_fitted)

        # Prediction phase
        predictions = model.predict(self.test_data)
        
        # Validate output
        self.assertEqual(predictions.shape, self.test_data.shape)
        self.assertFalse(torch.isnan(predictions).any())  # No NaN in output

        # Test model info
        info = model.get_model_info()
        self.assertIn('model_name', info)
        self.assertIn('model_type', info)
        self.assertEqual(info['model_type'], 'advanced_deep_learning')

if __name__ == '__main__':
    print("🧪 Running Enhanced UNet Test Suite...")
    print("=" * 60)
    
    # Run tests
    unittest.main(verbosity=2)
